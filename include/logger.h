#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <vector>
#include <mutex>

#ifdef __cplusplus
extern "C" {
#endif

// ログレベル定義
typedef enum {
    LOG_LEVEL_INFO = 0,
    LOG_LEVEL_WARNING = 1,
    LOG_LEVEL_ERROR = 2,
    LOG_LEVEL_DEBUG = 3
} LogLevel;

// ログエントリ構造体
typedef struct {
    std::string message;
    LogLevel level;
    std::string timestamp;
} LogEntry;

// ログバッファクラス
class Logger {
private:
    static std::vector<LogEntry> log_buffer;
    static std::mutex log_mutex;
    static const size_t MAX_LOG_ENTRIES = 1000;
    
    static std::string get_timestamp();
    static const char* get_level_string(LogLevel level);

public:
    // ログメッセージを追加
    static void add_log(LogLevel level, const char* format, ...);
    
    // ログバッファを取得（ImGuiでの表示用）
    static const std::vector<LogEntry>& get_logs();
    
    // ログバッファをクリア
    static void clear_logs();
    
    // ログの数を取得
    static size_t get_log_count();
    
    // 便利関数
    static void info(const char* format, ...);
    static void warning(const char* format, ...);
    static void error(const char* format, ...);
    static void debug(const char* format, ...);
};

// C言語インターフェース
void logger_add_log(LogLevel level, const char* format, ...);
void logger_info(const char* format, ...);
void logger_warning(const char* format, ...);
void logger_error(const char* format, ...);
void logger_debug(const char* format, ...);
void logger_clear();

#ifdef __cplusplus
}
#endif

#endif // LOGGER_H
