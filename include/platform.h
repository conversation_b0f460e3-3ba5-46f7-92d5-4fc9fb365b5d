#ifndef PLATFORM_H
#define PLATFORM_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// プラットフォーム検出
#if defined(_WIN32) || defined(_WIN64)
    #define PLATFORM_WINDOWS
#elif defined(__linux__)
    #define PLATFORM_LINUX
#elif defined(__APPLE__)
    #define PLATFORM_MACOS
#endif

// キーコード定義（プラットフォーム非依存）
typedef enum {
    KEY_NUMPAD_0 = 0,
    KEY_NUMPAD_5 = 5,
    KEY_NUMPAD_9 = 9,
    KEY_ESCAPE = 27,
    KEY_SPACE = 32
} PlatformKey;

// マウスボタン定義
typedef enum {
    MOUSE_BUTTON_LEFT = 0,
    MOUSE_BUTTON_RIGHT = 1,
    MOUSE_BUTTON_MIDDLE = 2
} MouseButton;

// マウス位置構造体
typedef struct {
    int x;
    int y;
} MousePosition;

// プラットフォーム抽象化関数

/**
 * プラットフォーム固有の初期化を行う
 * @return 成功時true、失敗時false
 */
bool platform_init(void);

/**
 * プラットフォーム固有のクリーンアップを行う
 */
void platform_cleanup(void);

/**
 * 指定されたキーが押されたかどうかを確認する
 * @param key 確認するキー
 * @return キーが押された場合true
 */
bool platform_key_pressed(PlatformKey key);

/**
 * マウスクリックを実行する
 * @param button クリックするマウスボタン
 * @return 成功時true、失敗時false
 */
bool platform_mouse_click(MouseButton button);

/**
 * 現在のマウス位置を取得する
 * @param pos マウス位置を格納する構造体のポインタ
 * @return 成功時true、失敗時false
 */
bool platform_get_mouse_position(MousePosition* pos);

/**
 * 高精度な待機を行う
 * @param nanoseconds 待機時間（ナノ秒）
 */
void platform_nano_sleep(int64_t nanoseconds);

/**
 * ミリ秒単位の待機を行う
 * @param milliseconds 待機時間（ミリ秒）
 */
void platform_sleep(int milliseconds);

/**
 * 高精度タイマーの現在時刻を取得する（ナノ秒）
 * @return 現在時刻（ナノ秒）
 */
int64_t platform_get_time_ns(void);

#ifdef __cplusplus
}
#endif

#endif // PLATFORM_H
