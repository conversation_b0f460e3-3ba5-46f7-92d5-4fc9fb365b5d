{
    errdata = "In file included from \27[01m\27[Ksrc/core/main.cpp:8\27[m\27[K:\
\27[01m\27[Kinclude/platform.h:15:13:\27[m\27[K \27[01;35m\27[Kwarning: \27[m\27[K\"PLATFORM_LINUX\" redefined\
   15 |     #define \27[01;35m\27[KPLATFORM_LINUX\27[m\27[K\
      |             \27[01;35m\27[K^~~~~~~~~~~~~~\27[m\27[K\
\27[01m\27[K<command-line>:\27[m\27[K \27[01;36m\27[Knote: \27[m\27[Kthis is the location of the previous definition\
In file included from \27[01m\27[Ksrc/core/main.cpp:8\27[m\27[K:\
\27[01m\27[Kinclude/platform.h:15:9:\27[m\27[K \27[01;35m\27[Kwarning: \27[m\27[K\"PLATFORM_LINUX\" redefined\
   15 |     #def\27[01;35m\27[Kine PLATFORM_L\27[m\27[KINUX\
      |         \27[01;35m\27[K^~~~~~~~~~~~~~\27[m\27[K\
\27[01m\27[K<command-line>:\27[m\27[K \27[01;36m\27[Knote: \27[m\27[Kthis is the location of the previous definition\
\27[01m\27[Kcc1plus:\27[m\27[K \27[01;36m\27[Knote: \27[m\27[Kunrecognized command-line option ‘\27[01m\27[K-Wno-gnu-line-marker\27[m\27[K’ may have been intended to silence earlier diagnostics\
"
}