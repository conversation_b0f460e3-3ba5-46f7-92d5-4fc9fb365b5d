{
    files = {
        "build/.objs/autoclicker/linux/x86_64/debug/src/core/logger.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/core/main.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/core/platform_linux.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/core/platform_windows.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_draw.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_impl_glfw.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_impl_opengl3.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_tables.cpp.o",
        "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_widgets.cpp.o"
    },
    values = {
        "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++",
        {
            "-m64",
            "-lGL",
            "-lX11",
            "-lXtst",
            "-lXext",
            "-lglfw",
            "-lpthread",
            "-lrt",
            "-O3",
            "-g3"
        }
    }
}