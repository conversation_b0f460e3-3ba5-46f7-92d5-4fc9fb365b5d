{
    files = {
        "src/core/platform_windows.cpp"
    },
    values = {
        "/run/current-system/sw/bin/gcc",
        {
            "-m64",
            "-g",
            "-O0",
            "-std=c++17",
            "-Iinclude",
            "-Iinclude/imgui",
            "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c",
            "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl",
            "-DDEBUG",
            "-DDEBUG_GL",
            "-DGL_DEBUG",
            "-DPLATFORM_DESKTOP",
            "-DIMGUI_IMPL_OPENGL_LOADER_GLEW",
            "-O3",
            "-g3",
            "-fstack-protector-all",
            "-fno-omit-frame-pointer"
        }
    },
    depfiles_format = "gcc",
    depfiles = "platform_windows.o: src/core/platform_windows.cpp\
"
}