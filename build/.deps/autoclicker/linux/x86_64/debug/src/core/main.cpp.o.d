{
    values = {
        "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++",
        {
            "-m64",
            "-g",
            "-O0",
            "-std=c++17",
            "-Iinclude",
            "-Iinclude/imgui",
            "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c",
            "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl",
            "-DDEBUG",
            "-DDEBUG_GL",
            "-DGL_DEBUG",
            "-DPLATFORM_DESKTOP",
            "-DPLATFORM_LINUX",
            "-DIMGUI_IMPL_OPENGL_LOADER_GL3W",
            "-O3",
            "-g3",
            "-fstack-protector-all",
            "-fno-omit-frame-pointer"
        }
    },
    depfiles = "main.o: src/core/main.cpp include/platform.h include/logger.h  include/imgui/imgui.h include/imgui/imconfig.h  include/imgui/imgui_impl_glfw.h include/imgui/imgui_impl_opengl3.h\
",
    files = {
        "src/core/main.cpp"
    },
    depfiles_format = "gcc"
}