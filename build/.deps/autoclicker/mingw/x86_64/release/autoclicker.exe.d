{
    files = {
        [[build\.objs\autoclicker\mingw\x86_64\release\src\core\main.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui_draw.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui_impl_glfw.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui_impl_opengl3.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui_tables.cpp.obj]],
        [[build\.objs\autoclicker\mingw\x86_64\release\src\imgui\imgui_widgets.cpp.obj]]
    },
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-m64",
            [[-LC:\msys64\mingw64\bin\..\lib]],
            "-s",
            "-lglu32",
            "-ljpeg",
            "-lpng",
            "-lwebp",
            "-lwebpdemux",
            "-lwebpmux",
            "-liconv",
            "-lfontconfig",
            "-lfreetype",
            "-lz",
            "-lexpat",
            "-lglfw3",
            "-lopengl32",
            "-lglew32",
            "-lole32",
            "-luser32",
            "-lgdi32",
            "-lcomdlg32",
            "-lpthread",
            "-luuid",
            "-O3",
            "-mtune=native",
            "-march=native",
            "-mfpmath=both",
            "-fopenmp"
        }
    }
}