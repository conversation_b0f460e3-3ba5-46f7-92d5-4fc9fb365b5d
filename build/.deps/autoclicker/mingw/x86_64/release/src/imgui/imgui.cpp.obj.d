{
    depfiles_format = "gcc",
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-m64",
            "-fvisibility=hidden",
            "-fvisibility-inlines-hidden",
            "-O3",
            "-std=c++17",
            "-Iinclude",
            [[-Iinclude\core]],
            [[-Iinclude\imgui]],
            [[-Ibuild\.gens\autoclicker\mingw\x86_64\release\rules\utils\bin2c]],
            [[-IC:\msys64\mingw64\bin\..\include\freetype2]],
            [[-IC:\msys64\mingw64\bin\..\include\libpng16]],
            [[-IC:\msys64\mingw64\bin\..\include\harfbuzz]],
            [[-IC:\msys64\mingw64\bin\..\include\glib-2.0]],
            [[-IC:\msys64\mingw64\bin\..\lib\glib-2.0\include]],
            [[-IC:\msys64\mingw64\bin\..\include]],
            "-DPLATFORM_DESKTOP",
            "-DIMGUI_IMPL_OPENGL_LOADER_GLEW",
            "-O3",
            "-mtune=native",
            "-march=native",
            "-mfpmath=both",
            "-DNDEBUG"
        }
    },
    depfiles = "imgui.o: src\\imgui\\imgui.cpp include\\imgui/imgui.h  include\\imgui/imconfig.h include\\imgui/imgui_internal.h\
",
    files = {
        [[src\imgui\imgui.cpp]]
    }
}