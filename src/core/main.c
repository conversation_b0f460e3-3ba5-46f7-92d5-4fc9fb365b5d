#include <windows.h>
#include <stdio.h>
#include <stdbool.h>

#define TOGGLE_KEY VK_NUMPAD0  // テンキーの0キーでオートクリックの開始/停止を切り替え
#define EXIT_KEY VK_NUMPAD9  // テンキーの9キーで終了
#define SWITCH_BUTTON_KEY VK_NUMPAD5  // テンキーの5キーでクリックボタン（左/右）を切り替え

// クリックモード定数
#define LEFT_CLICK_MODE 0
#define RIGHT_CLICK_MODE 1

// 高精度タイマー用の定数
#define NANO_SECOND 1000000000LL

// オートクリッカーの設定
typedef struct {
    int clicks_per_second;  // 1秒あたりのクリック数
    bool is_running;        // クリック実行中かどうか
    bool should_exit;       // プログラムを終了するかどうか
    int click_mode;         // クリックモード（0:左、1:右）
} ClickerConfig;

// キーの状態を追跡する
bool key_was_pressed(int key) {
    static bool previous_state[256] = {false};
    bool current_state = GetAsyncKeyState(key) & 0x8000;
    bool result = current_state && !previous_state[key];
    previous_state[key] = current_state;
    return result;
}

// ナノ秒単位での高精度な待機を実行する関数
void nano_sleep(long long nanoseconds) {
    static LARGE_INTEGER frequency;
    static int init = 0;
    
    if (!init) {
        QueryPerformanceFrequency(&frequency);
        init = 1;
    }
    
    LARGE_INTEGER start, current;
    QueryPerformanceCounter(&start);
    
    // 周波数から待機時間（カウント数）を計算
    long long wait_count = (nanoseconds * frequency.QuadPart) / NANO_SECOND;
    
    // 指定された時間が経過するまで待機
    do {
        QueryPerformanceCounter(&current);
    } while (current.QuadPart - start.QuadPart < wait_count);
}

// オートクリック実行関数
void perform_click(int click_mode) {
    // マウスの現在位置を取得
    POINT cursor_pos;
    GetCursorPos(&cursor_pos);
    
    if (click_mode == LEFT_CLICK_MODE) {
        // マウスの左ボタンを押す
        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
        // 少し待機して確実にクリックを認識させる
        Sleep(1);
        // マウスの左ボタンを離す
        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
    } else {
        // マウスの右ボタンを押す
        mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
        // 少し待機して確実にクリックを認識させる
        Sleep(1);
        // マウスの右ボタンを離す
        mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
    }
}

int main() {    
    ClickerConfig config = {
        .clicks_per_second = 20,  // より現実的なクリック速度に調整
        .is_running = false,
        .should_exit = false,
        .click_mode = RIGHT_CLICK_MODE  // デフォルトは右クリック
    };
    printf("Auto-clicker successfully started!\n");
    printf("Numpad 0: Start/Stop clicking\n");
    printf("Numpad 5: Switch between left/right click mode\n");
    printf("Numpad 9: Exit program\n");
    printf("Clicking speed: %d clicks/second\n", config.clicks_per_second);
    printf("Current mode: %s click\n", config.click_mode == LEFT_CLICK_MODE ? "LEFT" : "RIGHT");    // メインループ
    while (!config.should_exit) {
        // トグルキーが押されたらクリック状態を切り替え        
        if (key_was_pressed(TOGGLE_KEY)) {
            config.is_running = !config.is_running;            
            
            if (config.is_running) {
                printf("Clicking started!\n");
            } else {
                printf("Clicking stopped!\n");
            }
        }
        
        // クリックモード切り替えキーが押されたらモードを切り替え
        if (key_was_pressed(SWITCH_BUTTON_KEY)) {
            config.click_mode = (config.click_mode == LEFT_CLICK_MODE) ? RIGHT_CLICK_MODE : LEFT_CLICK_MODE;
            printf("Switched to %s click mode!\n", config.click_mode == LEFT_CLICK_MODE ? "LEFT" : "RIGHT");
        }
          // 終了キーが押されたらループを抜ける
        if (key_was_pressed(EXIT_KEY)) {
            printf("Exiting program. Goodbye!\n");
            config.should_exit = true;
        }        // 実行中であればクリック処理
        if (config.is_running) {
            perform_click(config.click_mode);
            
            // クリック間隔を計算（ナノ秒単位）
            long long delay_ns = (1000000000LL / config.clicks_per_second);
            nano_sleep(delay_ns);
        } else {
            // 非実行中はCPU使用率を下げるために少し待機
            Sleep(10);
        }
    }
    
    return 0;
}