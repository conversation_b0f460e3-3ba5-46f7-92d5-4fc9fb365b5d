#ifdef PLATFORM_WINDOWS

#include "platform.h"
#include <windows.h>
#include <stdio.h>

// キーの状態を追跡する配列
static bool previous_key_state[256] = {false};

// Windows固有のキーコードマッピング
static int get_windows_keycode(PlatformKey key) {
    switch (key) {
        case KEY_NUMPAD_0: return VK_NUMPAD0;
        case KEY_NUMPAD_5: return VK_NUMPAD5;
        case KEY_NUMPAD_9: return VK_NUMPAD9;
        case KEY_ESCAPE: return VK_ESCAPE;
        case KEY_SPACE: return VK_SPACE;
        default: return 0;
    }
}

bool platform_init(void) {
    // Windows固有の初期化処理
    return true;
}

void platform_cleanup(void) {
    // Windows固有のクリーンアップ処理
}

bool platform_key_pressed(PlatformKey key) {
    int vk_code = get_windows_keycode(key);
    if (vk_code == 0) return false;
    
    bool current_state = GetAsyncKeyState(vk_code) & 0x8000;
    bool result = current_state && !previous_key_state[vk_code];
    previous_key_state[vk_code] = current_state;
    return result;
}

bool platform_mouse_click(MouseButton button) {
    POINT cursor_pos;
    if (!GetCursorPos(&cursor_pos)) {
        return false;
    }
    
    switch (button) {
        case MOUSE_BUTTON_LEFT:
            mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
            Sleep(1);
            mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
            break;
        case MOUSE_BUTTON_RIGHT:
            mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
            Sleep(1);
            mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
            break;
        case MOUSE_BUTTON_MIDDLE:
            mouse_event(MOUSEEVENTF_MIDDLEDOWN, 0, 0, 0, 0);
            Sleep(1);
            mouse_event(MOUSEEVENTF_MIDDLEUP, 0, 0, 0, 0);
            break;
        default:
            return false;
    }
    return true;
}

bool platform_get_mouse_position(MousePosition* pos) {
    if (!pos) return false;
    
    POINT cursor_pos;
    if (!GetCursorPos(&cursor_pos)) {
        return false;
    }
    
    pos->x = cursor_pos.x;
    pos->y = cursor_pos.y;
    return true;
}

void platform_nano_sleep(int64_t nanoseconds) {
    static LARGE_INTEGER frequency;
    static int init = 0;
    
    if (!init) {
        QueryPerformanceFrequency(&frequency);
        init = 1;
    }
    
    LARGE_INTEGER start, current;
    QueryPerformanceCounter(&start);
    
    // 周波数から待機時間（カウント数）を計算
    int64_t wait_count = (nanoseconds * frequency.QuadPart) / 1000000000LL;
    
    // 指定された時間が経過するまで待機
    do {
        QueryPerformanceCounter(&current);
    } while (current.QuadPart - start.QuadPart < wait_count);
}

void platform_sleep(int milliseconds) {
    Sleep(milliseconds);
}

int64_t platform_get_time_ns(void) {
    static LARGE_INTEGER frequency;
    static int init = 0;
    
    if (!init) {
        QueryPerformanceFrequency(&frequency);
        init = 1;
    }
    
    LARGE_INTEGER counter;
    QueryPerformanceCounter(&counter);
    
    // カウンターをナノ秒に変換
    return (counter.QuadPart * 1000000000LL) / frequency.QuadPart;
}

#endif // PLATFORM_WINDOWS
