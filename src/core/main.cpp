#include <stdio.h>
#include <stdbool.h>
#include <string>
#include "platform.h"
#include "logger.h"

#ifdef PLATFORM_WINDOWS
#include <Windows.h>
#endif

// ImGuiの設定
#include "imgui.h"
#include "imgui_impl_glfw.h"
#include "imgui_impl_opengl3.h"
#ifdef PLATFORM_LINUX
    #include <GL/gl.h>
    #include <GLFW/glfw3.h>
#else
    #include <GL/glew.h>
    #include <GLFW/glfw3.h>
#endif

// クリックモード定数
#define LEFT_CLICK_MODE 0
#define RIGHT_CLICK_MODE 1

// 高精度タイマー用の定数
#define NANO_SECOND 1000000000LL

// オートクリッカーの設定
struct ClickerConfig {
    int clicks_per_second;  // 1秒あたりのクリック数
    bool is_running;        // クリック実行中かどうか
    bool should_exit;       // プログラムを終了するかどうか
    int click_mode;         // クリックモード（0:左、1:右）
    bool show_gui;          // GUIを表示するかどうか
};

// プラットフォーム固有のキー入力検出をラップする関数
bool key_was_pressed(<PERSON><PERSON><PERSON> key) {
    return platform_key_pressed(key);
}

// ナノ秒単位での高精度な待機を実行する関数
void nano_sleep(long long nanoseconds) {
    platform_nano_sleep(nanoseconds);
}

// オートクリック実行関数
void perform_click(int click_mode) {
    MouseButton button;
    if (click_mode == LEFT_CLICK_MODE) {
        button = MOUSE_BUTTON_LEFT;
    } else {
        button = MOUSE_BUTTON_RIGHT;
    }

    platform_mouse_click(button);
}

// ImGuiのウィンドウの初期化
static void glfw_error_callback(int error, const char* description) {
    fprintf(stderr, "GLFW Error %d: %s\n", error, description);
}

// ログウィンドウを表示する関数
void RenderLogWindow(bool* show_log_window) {
    if (!*show_log_window) return;

    ImGui::SetNextWindowSize(ImVec2(600, 400), ImGuiCond_FirstUseEver);
    ImGui::SetNextWindowPos(ImVec2(450, 50), ImGuiCond_FirstUseEver);

    if (ImGui::Begin("Log Window", show_log_window)) {
        // クリアボタン
        if (ImGui::Button("Clear Logs")) {
            Logger::clear_logs();
        }

        ImGui::SameLine();
        ImGui::Text("Total logs: %zu", Logger::get_log_count());

        ImGui::Separator();

        // ログ表示エリア
        ImGui::BeginChild("LogScrolling", ImVec2(0, 0), false, ImGuiWindowFlags_HorizontalScrollbar);

        const auto& logs = Logger::get_logs();
        for (const auto& log : logs) {
            // ログレベルに応じて色を変更
            ImVec4 color;
            switch (log.level) {
                case LOG_LEVEL_ERROR:
                    color = ImVec4(1.0f, 0.4f, 0.4f, 1.0f); // 赤
                    break;
                case LOG_LEVEL_WARNING:
                    color = ImVec4(1.0f, 1.0f, 0.4f, 1.0f); // 黄
                    break;
                case LOG_LEVEL_DEBUG:
                    color = ImVec4(0.7f, 0.7f, 0.7f, 1.0f); // グレー
                    break;
                case LOG_LEVEL_INFO:
                default:
                    color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f); // 白
                    break;
            }

            ImGui::PushStyleColor(ImGuiCol_Text, color);
            ImGui::TextWrapped("[%s] %s", log.timestamp.c_str(), log.message.c_str());
            ImGui::PopStyleColor();
        }

        // 自動スクロール
        if (ImGui::GetScrollY() >= ImGui::GetScrollMaxY()) {
            ImGui::SetScrollHereY(1.0f);
        }

        ImGui::EndChild();
    }
    ImGui::End();
}

// ImGuiウィンドウに表示するUI
void RenderUI(ClickerConfig* config, bool* show_log_window) {
    // ウィンドウのサイズと位置を設定
    ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);
    ImGui::SetNextWindowSize(ImVec2(350, 250), ImGuiCond_FirstUseEver);

    // ウィンドウの開始
    ImGui::Begin("Auto Clicker Settings", nullptr, ImGuiWindowFlags_AlwaysAutoResize);
    
    // クリック速度スライダー
    ImGui::Text("Click Speed:");
    ImGui::SliderInt("CPS", &config->clicks_per_second, 1, 1000, "%d clicks/sec");
    
    // クリックモード選択
    const char* click_modes[] = { "Left Click", "Right Click" };
    ImGui::Combo("Click Mode", &config->click_mode, click_modes, IM_ARRAYSIZE(click_modes));
    
    // 開始/停止ボタン
    if (ImGui::Button(config->is_running ? "Stop Clicking" : "Start Clicking", ImVec2(150, 40))) {
        config->is_running = !config->is_running;
    }

    ImGui::SameLine();

    // 終了ボタン
    if (ImGui::Button("Exit", ImVec2(150, 40))) {
        config->should_exit = true;
    }

    // ログウィンドウ表示ボタン
    if (ImGui::Button("Show Log Window", ImVec2(150, 30))) {
        *show_log_window = true;
    }
    
    // ホットキー情報
    ImGui::Separator();
    ImGui::Text("Hotkeys:");
    ImGui::BulletText("Numpad 0: Start/Stop clicking");
    ImGui::BulletText("Numpad 5: Switch between left/right click");
    ImGui::BulletText("Numpad 9: Exit program");
    
    // ステータス表示
    ImGui::Separator();
    ImGui::Text("Status: %s", config->is_running ? "Running" : "Stopped");
    ImGui::Text("Current Mode: %s", config->click_mode == LEFT_CLICK_MODE ? "LEFT" : "RIGHT");
    
    // ウィンドウの終了
    ImGui::End();
}

// プラットフォーム固有の初期化
bool platform_specific_init() {
#ifdef PLATFORM_WINDOWS
    // コンソールウィンドウを作成（デバッグ用）
    AllocConsole();
    FILE* consoleOut = nullptr;
    freopen_s(&consoleOut, "CONOUT$", "w", stdout);
#endif
    return platform_init();
}

// クロスプラットフォーム対応のメイン関数
#ifdef PLATFORM_WINDOWS
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nShowCmd) {
#else
int main(int argc, char* argv[]) {
#endif
    // プラットフォーム固有の初期化
    if (!platform_specific_init()) {
        fprintf(stderr, "Failed to initialize platform\n");
        return 1;
    }

    // GLFWの初期化
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        return 1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // ウィンドウの作成
    GLFWwindow* window = glfwCreateWindow(400, 300, "Auto Clicker", NULL, NULL);
    if (window == NULL) {
        glfwTerminate();
        return 1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // VSync有効

    // OpenGLの初期化（プラットフォーム別）
#ifndef PLATFORM_LINUX
    // GLEWの初期化（Windows用）
    GLenum err = glewInit();
    if (err != GLEW_OK) {
        fprintf(stderr, "Failed to initialize GLEW: %s\n", glewGetErrorString(err));
        return 1;
    }
#endif

    // ImGuiのコンテキスト初期化
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;

    // ImGuiのスタイル設定
    ImGui::StyleColorsDark();

    // プラットフォームバックエンドの初期化
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // オートクリッカー設定の初期化
    ClickerConfig config = {
        .clicks_per_second = 20,
        .is_running = false,
        .should_exit = false,
        .click_mode = RIGHT_CLICK_MODE,
        .show_gui = true
    };

    // ログウィンドウの表示状態
    bool show_log_window = false;

    Logger::info("Auto-clicker successfully started with GUI!");

    // メインループ
    while (!glfwWindowShouldClose(window) && !config.should_exit) {
        // フレームの開始
        glfwPollEvents();
        
        // ImGuiフレームの開始
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // ホットキーの処理
        if (key_was_pressed(KEY_NUMPAD_0)) {  // テンキーの0
            config.is_running = !config.is_running;
            Logger::info("Clicking %s!", config.is_running ? "started" : "stopped");
        }

        if (key_was_pressed(KEY_NUMPAD_5)) {  // テンキーの5
            config.click_mode = (config.click_mode == LEFT_CLICK_MODE) ? RIGHT_CLICK_MODE : LEFT_CLICK_MODE;
            Logger::info("Switched to %s click mode!", config.click_mode == LEFT_CLICK_MODE ? "LEFT" : "RIGHT");
        }

        if (key_was_pressed(KEY_NUMPAD_9)) {  // テンキーの9
            Logger::info("Exiting program. Goodbye!");
            config.should_exit = true;
        }

        // クリック処理
        if (config.is_running && !io.WantCaptureMouse) {
            perform_click(config.click_mode);
            
            // クリック間隔を計算（ナノ秒単位）
            long long delay_ns = (1000000000LL / config.clicks_per_second);
            nano_sleep(delay_ns);
        }
        
        // GUI描画
        RenderUI(&config, &show_log_window);
        RenderLogWindow(&show_log_window);
        
        // ImGuiのレンダリング
        ImGui::Render();
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        glClearColor(0.1f, 0.1f, 0.1f, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT);
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        glfwSwapBuffers(window);
    }
    
    // 終了処理
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    // プラットフォーム固有のクリーンアップ
    platform_cleanup();

    return 0;
}
