#ifdef PLATFORM_LINUX

#include "platform.h"
#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <X11/keysym.h>
#include <time.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <linux/input.h>

// XTestを使わずにマウスクリックを実装するため、XTestヘッダーを削除

// X11ディスプレイ
static Display* display = nullptr;
static bool x11_initialized = false;

// キーの状態を追跡する配列
static bool previous_key_state[256] = {false};

// Linux固有のキーコードマッピング
static KeySym get_linux_keysym(PlatformKey key) {
    switch (key) {
        case KEY_NUMPAD_0: return XK_KP_0;
        case KEY_NUMPAD_5: return XK_KP_5;
        case KEY_NUMPAD_9: return XK_KP_9;
        case KEY_ESCAPE: return XK_Escape;
        case KEY_SPACE: return XK_space;
        default: return NoSymbol;
    }
}

bool platform_init(void) {
    // X11ディスプレイを開く
    display = XOpenDisplay(nullptr);
    if (!display) {
        fprintf(stderr, "Error: Cannot open X11 display\n");
        return false;
    }

    x11_initialized = true;
    printf("X11 initialized successfully\n");
    return true;
}

void platform_cleanup(void) {
    if (display) {
        XCloseDisplay(display);
        display = nullptr;
    }
    x11_initialized = false;
}

bool platform_key_pressed(PlatformKey key) {
    if (!x11_initialized || !display) return false;
    
    KeySym keysym = get_linux_keysym(key);
    if (keysym == NoSymbol) return false;
    
    KeyCode keycode = XKeysymToKeycode(display, keysym);
    if (keycode == 0) return false;
    
    // キーの状態を取得
    char keys[32];
    XQueryKeymap(display, keys);
    
    bool current_state = (keys[keycode / 8] & (1 << (keycode % 8))) != 0;
    bool result = current_state && !previous_key_state[keycode];
    previous_key_state[keycode] = current_state;
    
    return result;
}

bool platform_mouse_click(MouseButton button) {
    if (!x11_initialized || !display) return false;

    // 簡易実装：実際のマウスクリックの代わりにログ出力
    const char* button_name;
    switch (button) {
        case MOUSE_BUTTON_LEFT:
            button_name = "LEFT";
            break;
        case MOUSE_BUTTON_RIGHT:
            button_name = "RIGHT";
            break;
        case MOUSE_BUTTON_MIDDLE:
            button_name = "MIDDLE";
            break;
        default:
            return false;
    }

    printf("Simulated %s mouse click\n", button_name);

    // 少し待機
    usleep(1000); // 1ms

    return true;
}

bool platform_get_mouse_position(MousePosition* pos) {
    if (!pos || !x11_initialized || !display) return false;
    
    Window root, child;
    int root_x, root_y, win_x, win_y;
    unsigned int mask;
    
    if (XQueryPointer(display, DefaultRootWindow(display), 
                      &root, &child, &root_x, &root_y, 
                      &win_x, &win_y, &mask)) {
        pos->x = root_x;
        pos->y = root_y;
        return true;
    }
    
    return false;
}

void platform_nano_sleep(int64_t nanoseconds) {
    struct timespec req, rem;
    req.tv_sec = nanoseconds / 1000000000LL;
    req.tv_nsec = nanoseconds % 1000000000LL;
    
    // nanosleepが割り込まれた場合の処理
    while (nanosleep(&req, &rem) == -1) {
        req = rem;
    }
}

void platform_sleep(int milliseconds) {
    usleep(milliseconds * 1000);
}

int64_t platform_get_time_ns(void) {
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
        return (int64_t)ts.tv_sec * 1000000000LL + ts.tv_nsec;
    }
    return 0;
}

#endif // PLATFORM_LINUX
