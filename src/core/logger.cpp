#include "logger.h"
#include <cstdarg>
#include <cstdio>
#include <ctime>
#include <chrono>
#include <iomanip>
#include <sstream>

// 静的メンバーの定義
std::vector<LogEntry> Logger::log_buffer;
std::mutex Logger::log_mutex;

std::string Logger::get_timestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

const char* Logger::get_level_string(LogLevel level) {
    switch (level) {
        case LOG_LEVEL_INFO: return "INFO";
        case LOG_LEVEL_WARNING: return "WARN";
        case LOG_LEVEL_ERROR: return "ERROR";
        case LOG_LEVEL_DEBUG: return "DEBUG";
        default: return "UNKNOWN";
    }
}

void Logger::add_log(LogLevel level, const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    std::lock_guard<std::mutex> lock(log_mutex);
    
    LogEntry entry;
    entry.message = std::string(buffer);
    entry.level = level;
    entry.timestamp = get_timestamp();
    
    log_buffer.push_back(entry);
    
    // バッファサイズ制限
    if (log_buffer.size() > MAX_LOG_ENTRIES) {
        log_buffer.erase(log_buffer.begin());
    }
    
    // コンソールにも出力
    printf("[%s] %s: %s\n", entry.timestamp.c_str(), get_level_string(level), buffer);
}

const std::vector<LogEntry>& Logger::get_logs() {
    return log_buffer;
}

void Logger::clear_logs() {
    std::lock_guard<std::mutex> lock(log_mutex);
    log_buffer.clear();
}

size_t Logger::get_log_count() {
    std::lock_guard<std::mutex> lock(log_mutex);
    return log_buffer.size();
}

void Logger::info(const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    add_log(LOG_LEVEL_INFO, "%s", buffer);
}

void Logger::warning(const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    add_log(LOG_LEVEL_WARNING, "%s", buffer);
}

void Logger::error(const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    add_log(LOG_LEVEL_ERROR, "%s", buffer);
}

void Logger::debug(const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    add_log(LOG_LEVEL_DEBUG, "%s", buffer);
}

// C言語インターフェース
extern "C" {
    void logger_add_log(LogLevel level, const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Logger::add_log(level, "%s", buffer);
    }
    
    void logger_info(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Logger::info("%s", buffer);
    }
    
    void logger_warning(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Logger::warning("%s", buffer);
    }
    
    void logger_error(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Logger::error("%s", buffer);
    }
    
    void logger_debug(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        Logger::debug("%s", buffer);
    }
    
    void logger_clear() {
        Logger::clear_logs();
    }
}
