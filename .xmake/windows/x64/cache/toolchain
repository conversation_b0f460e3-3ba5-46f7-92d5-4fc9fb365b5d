{
    yasm_arch_x86_64_plat_mingw = {
        __checked = true,
        plat = "mingw",
        arch = "x86_64",
        __global = true
    },
    mingw_arch_x86_64_plat_mingw = {
        cross = "x86_64-w64-mingw32-",
        arch = "x86_64",
        __global = true,
        __checked = true,
        bindir = [[C:\msys64\mingw64\bin]],
        mingw = [[C:\msys64\mingw64]],
        plat = "mingw"
    },
    envs_arch_x86_64_plat_mingw = {
        __checked = true,
        plat = "mingw",
        arch = "x86_64",
        __global = true
    },
    fasm_arch_x86_64_plat_mingw = {
        __checked = true,
        plat = "mingw",
        arch = "x86_64",
        __global = true
    },
    gcc_arch_x64_plat_msys = {
        __checked = {
            program = [[C:\msys64\mingw64\bin\gcc.exe]],
            name = "gcc"
        },
        arch = "x64",
        plat = "msys"
    },
    go_arch_x64_plat_msys = {
        __checked = true,
        arch = "x64",
        plat = "msys"
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        arch = "x64",
        vs_toolset = "14.44.35207",
        __checked = "2022",
        vcvars = {
            VCToolsRedistDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Redist\MSVC\14.44.35112\]],
            INCLUDE = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\include;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um]],
            DevEnvdir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\]],
            VS170COMNTOOLS = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\]],
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\]],
            VCToolsInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\]],
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0]],
            VSCMD_ARG_app_plat = "Desktop",
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            VSCMD_ARG_TGT_ARCH = "x64",
            PATH = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\VCPackages;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\Extensions\Microsoft\CodeCoverage.Console;C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\;C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\4bf46eef4bbc49d49e59e81106446263\bin;C:\Program Files\PowerShell\7;C:\VulkanSDK\1.4.313.0\Bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Program Files\Microsoft\jdk-21.0.5.11-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Users\<USER>\Documents\platform-tools;C:\Program Files\Tailscale\;C:\msys64\mingw64\bin;C:\msys64\usr\bin;C:\Program Files\Youlean\Youlean Loudness Meter 2;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe;C:\Program Files\CMake\bin;C:\Program Files\Meson\;C:\Program Files (x86)\QuickTime\QTSystem\;C:\Program Files\PowerShell\7\;C:\Program Files\LLVM\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0\;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\ellama\bin\;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.ggh;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Fastfetch-cli.Fastfetch_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\xmake;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ninja-build.Ninja_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\zig.zig_Microsoft.Winget.Source_8wekyb3d8bbwe\zig-windows-x86_64-0.14.0;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\yt-dlp.yt-dlp_Microsoft.Winget.Source_8wekyb3d8bbwe;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\Llvm\x64\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\vcpkg]],
            VSCMD_VER = "17.14.4",
            VSCMD_ARG_HOST_ARCH = "x64",
            LIBPATH = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            VCIDEInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\]],
            VisualStudioVersion = "17.0",
            VSInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\]],
            VS140COMNTOOLS = [[C:\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\]],
            VCToolsVersion = "14.44.35207",
            UCRTVersion = "10.0.26100.0",
            VCInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\]],
            LIB = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64]],
            WindowsSDKVersion = "10.0.26100.0",
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]]
        },
        vs_sdkver = "10.0.26100.0",
        vs = "2022"
    },
    tool_target_autoclicker_mingw_x86_64_cc = {
        toolname = "gcc",
        toolchain_info = {
            name = "mingw",
            cachekey = "mingw_arch_x86_64_plat_mingw",
            arch = "x86_64",
            plat = "mingw"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    gfortran_arch_x64_plat_msys = {
        __checked = true,
        arch = "x64",
        plat = "msys"
    },
    tool_target_autoclicker_mingw_x86_64_cxx = {
        toolname = "gxx",
        toolchain_info = {
            name = "mingw",
            cachekey = "mingw_arch_x86_64_plat_mingw",
            arch = "x86_64",
            plat = "mingw"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    envs_arch_x64_plat_msys = {
        __checked = true,
        arch = "x64",
        plat = "msys"
    },
    nasm_arch_x86_64_plat_mingw = {
        __checked = true,
        plat = "mingw",
        arch = "x86_64",
        __global = true
    },
    yasm_arch_x64_plat_msys = {
        __checked = true,
        arch = "x64",
        plat = "msys"
    },
    go_arch_x86_64_plat_mingw = {
        __checked = true,
        plat = "mingw",
        arch = "x86_64",
        __global = true
    },
    tool_target_autoclicker_mingw_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "mingw",
            cachekey = "mingw_arch_x86_64_plat_mingw",
            arch = "x86_64",
            plat = "mingw"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    }
}