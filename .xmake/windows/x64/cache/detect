{
    find_program_mingw_arch_x86_64_plat_mingw_checktoolcxx = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--add-stdcall-alias"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--version"] = true,
            ["--just-symbols"] = true,
            ["--map-whole-files"] = true,
            ["-o"] = true,
            ["-F"] = true,
            ["--warn-textrel"] = true,
            ["-V"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--default-image-base-high"] = true,
            ["--dynamic-list"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--push-state"] = true,
            ["--enable-auto-image-base"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["-debug"] = true,
            ["--nmagic"] = true,
            ["--warn-common"] = true,
            ["-Bshareable"] = true,
            ["--section-alignment"] = true,
            ["--entry"] = true,
            ["--end-group"] = true,
            ["--major-os-version"] = true,
            ["--library"] = true,
            ["--filter"] = true,
            ["-init"] = true,
            ["--output-def"] = true,
            ["-Y"] = true,
            ["--file-alignment"] = true,
            ["--no-warnings"] = true,
            ["--require-defined"] = true,
            ["-O"] = true,
            ["-f"] = true,
            ["--no-warn-mismatch"] = true,
            ["-y"] = true,
            ["--no-undefined-version"] = true,
            ["--exclude-all-symbols"] = true,
            ["--gpsize"] = true,
            ["--pic-executable"] = true,
            ["--default-script"] = true,
            ["--error-unresolved-symbols"] = true,
            ["-Map"] = true,
            ["--major-subsystem-version"] = true,
            ["--no-define-common"] = true,
            ["--heap"] = true,
            ["--dynamic-list-data"] = true,
            ["-Tbss"] = true,
            ["-Ur"] = true,
            ["-flto"] = true,
            ["--strip-debug"] = true,
            ["--remap-inputs"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--enable-auto-import"] = true,
            ["--disable-reloc-section"] = true,
            ["--no-dynamic-linker"] = true,
            ["--warn-once"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--cref"] = true,
            ["-u"] = true,
            ["--retain-symbols-file"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--undefined"] = true,
            ["--default-image-base-low"] = true,
            ["-m"] = true,
            ["--section-ordering-file"] = true,
            ["-A"] = true,
            ["-T"] = true,
            ["--trace"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--split-by-reloc"] = true,
            ["--dynamic-linker"] = true,
            ["--auxiliary"] = true,
            ["--relocatable"] = true,
            ["--no-print-gc-sections"] = true,
            ["--warn-alternate-em"] = true,
            ["--trace-symbol"] = true,
            ["--disable-auto-import"] = true,
            ["--compat-implib"] = true,
            ["--as-needed"] = true,
            ["-rpath"] = true,
            ["--start-group"] = true,
            ["-c"] = true,
            ["--no-check-sections"] = true,
            ["--omagic"] = true,
            ["--version-script"] = true,
            ["--no-export-dynamic"] = true,
            ["--sort-section"] = true,
            ["--remap-inputs-file"] = true,
            ["--enable-long-section-names"] = true,
            ["--disable-linker-version"] = true,
            ["--no-whole-archive"] = true,
            ["--no-print-map-locals"] = true,
            ["--dependency-file"] = true,
            ["--exclude-symbols"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["-Qy"] = true,
            ["-l"] = true,
            ["-Bno-symbolic"] = true,
            ["-assert"] = true,
            ["-plugin-opt"] = true,
            ["-nostdlib"] = true,
            ["--no-gc-sections"] = true,
            ["--orphan-handling"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--undefined-version"] = true,
            ["--no-print-map-discarded"] = true,
            ["--no-fatal-warnings"] = true,
            ["--kill-at"] = true,
            ["--no-strip-discarded"] = true,
            ["--no-omagic"] = true,
            ["--subsystem"] = true,
            ["-a"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["--output"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--export-all-symbols"] = true,
            ["-G"] = true,
            ["--default-symver"] = true,
            ["--architecture"] = true,
            ["--sort-common"] = true,
            ["--no-demangle"] = true,
            ["--image-base"] = true,
            ["--print-map-discarded"] = true,
            ["--no-as-needed"] = true,
            ["--print-gc-sections"] = true,
            ["--format"] = true,
            ["--exclude-libs"] = true,
            ["--gc-keep-exported"] = true,
            ["--ctf-variables"] = true,
            ["--traditional-format"] = true,
            ["-e"] = true,
            ["-R"] = true,
            ["--warn-section-align"] = true,
            ["-static"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["-EL"] = true,
            ["--minor-subsystem-version"] = true,
            ["--minor-image-version"] = true,
            ["--no-keep-memory"] = true,
            ["--strip-all"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--unique"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--print-memory-usage"] = true,
            ["-no-pie"] = true,
            ["-Tdata"] = true,
            ["--no-map-whole-files"] = true,
            ["--pop-state"] = true,
            ["--relax"] = true,
            ["--stack"] = true,
            ["--print-map-locals"] = true,
            ["--whole-archive"] = true,
            ["--defsym"] = true,
            ["--allow-multiple-definition"] = true,
            ["--print-sysroot"] = true,
            ["-plugin"] = true,
            ["--gc-sections"] = true,
            ["-fini"] = true,
            ["-Trodata-segment"] = true,
            ["--script"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--mri-script"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--disable-long-section-names"] = true,
            ["-plugin-save-temps"] = true,
            ["--wrap"] = true,
            ["--minor-os-version"] = true,
            ["--stats"] = true,
            ["--warn-multiple-gp"] = true,
            ["--discard-all"] = true,
            ["--no-ctf-variables"] = true,
            ["-Bsymbolic"] = true,
            ["--oformat"] = true,
            ["-b"] = true,
            ["--split-by-file"] = true,
            ["--enable-reloc-section"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--discard-locals"] = true,
            ["--fatal-warnings"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--export-dynamic"] = true,
            ["-Ttext-segment"] = true,
            ["-dT"] = true,
            ["--large-address-aware"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-Tldata-segment"] = true,
            ["-Ttext"] = true,
            ["-L"] = true,
            ["--print-map"] = true,
            ["--target-help"] = true,
            ["-qmagic"] = true,
            ["--default-imported-symver"] = true,
            ["--error-handling-script"] = true,
            ["-EB"] = true,
            ["--version-exports-section"] = true,
            ["--disable-large-address-aware"] = true,
            ["--no-relax"] = true,
            ["--major-image-version"] = true,
            ["--disable-auto-image-base"] = true,
            ["--demangle"] = true,
            ["--dll"] = true,
            ["--out-implib"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--library-path"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["-dp"] = true,
            ["--strip-discarded"] = true,
            ["--discard-none"] = true,
            ["-rpath-link"] = true,
            ["--emit-relocs"] = true,
            ["--section-start"] = true,
            ["-I"] = true,
            ["--help"] = true,
            ["-g"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--no-undefined"] = true,
            ["--task-link"] = true,
            ["-soname"] = true,
            ["-h"] = true,
            ["--check-sections"] = true,
            ["--enable-linker-version"] = true,
            ["--force-exe-suffix"] = true,
            ["--verbose"] = true,
            ["--print-output-format"] = true,
            ["--force-group-allocation"] = true,
            ["--support-old-code"] = true
        }
    },
    find_program_fetch_package_xmake = {
        gperf = false,
        python = false,
        python3 = false,
        cmake = false,
        ninja = false,
        meson = false,
        python2 = false
    },
    find_program = {
        emerge = false,
        git = [[C:\Program Files\Git\cmd\git.exe]],
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]],
        pacman = [[C:\msys64\usr\bin\pacman.exe]],
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        cygpath = [[C:\msys64\usr\bin\cygpath.exe]],
        ["cl.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        ["pkg-config"] = [[C:\msys64\mingw64\bin\pkg-config.exe]],
        gcc = [[C:\msys64\mingw64\bin\gcc.exe]]
    },
    find_program_mingw_arch_x86_64_plat_mingw_checktoolld = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    ["lib.detect.has_flags"] = {
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx__-m64_-O3"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags_-m64_-DNDEBUG"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags_-m64 -m64_-fopenmp"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxxflags_-m64_-O3 -mtune=native -march=native -mfpmath=both"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-gcc_15.1.0_cc__-m64_-O3 -mtune=native -march=native -mfpmath=both"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-gcc_15.1.0_cc__-m64_-fopenmp"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld__-m64 -m64_-fopenmp"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags_-m64_-fvisibility-inlines-hidden"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags_-m64 -m64_-O3 -mtune=native -march=native -mfpmath=both"] = true,
        ["mingw_x86_64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld__-m64 -m64_-O3 -mtune=native -march=native -mfpmath=both"] = true
    },
    find_program_fetch_package_system = {
        gperf = false,
        python3 = [[C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\python3.10.exe]],
        cmake = [[C:\Program Files\CMake\bin\cmake]],
        meson = [[C:\Program Files\Meson\meson.exe]],
        ninja = [[C:\msys64\mingw64\bin\ninja.exe]]
    },
    find_package_mingw_x86_64_fetch_package_system = {
        ["pacman::zlib_c7c23c5e1518475f91a7172443771802_release"] = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "1.3.1",
            includedirs = {
                "C:/msys64/mingw64/bin/../include"
            },
            libfiles = {
                [[C:\msys64\mingw64\lib\libz.a]],
                [[C:\msys64\mingw64\lib\libz.dll.a]]
            },
            links = {
                "z"
            }
        },
        fontconfig_62e53a1d83bd4fe388dd364c9b5903d6_release = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "2.16.2",
            includedirs = {
                "C:/msys64/mingw64/bin/../include",
                "C:/msys64/mingw64/bin/../include/freetype2",
                "C:/msys64/mingw64/bin/../include/libpng16",
                "C:/msys64/mingw64/bin/../include/harfbuzz",
                "C:/msys64/mingw64/bin/../include/glib-2.0",
                "C:/msys64/mingw64/bin/../lib/glib-2.0/include"
            },
            libfiles = {
                [[C:\msys64\mingw64\bin\..\lib\libfontconfig.a]]
            },
            links = {
                "fontconfig"
            }
        },
        ["pacman::freetype_ea1882b923ab4271ad20e28023169ad1_release"] = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "26.2.20",
            includedirs = {
                "C:/msys64/mingw64/bin/../include/freetype2",
                "C:/msys64/mingw64/bin/../include",
                "C:/msys64/mingw64/bin/../include/libpng16",
                "C:/msys64/mingw64/bin/../include/harfbuzz",
                "C:/msys64/mingw64/bin/../include/glib-2.0",
                "C:/msys64/mingw64/bin/../lib/glib-2.0/include"
            },
            libfiles = {
                [[C:\msys64\mingw64\lib\libfreetype.a]],
                [[C:\msys64\mingw64\lib\libfreetype.dll.a]]
            },
            links = {
                "freetype"
            }
        },
        glfw_2b0ee41504834bce92ad7417ca255952_release = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "3.4.0",
            includedirs = {
                "C:/msys64/mingw64/bin/../include"
            },
            libfiles = {
                [[C:\msys64\mingw64\lib\libglfw3.a]],
                [[C:\msys64\mingw64\lib\libglfw3.dll.a]]
            },
            links = {
                "glfw3"
            }
        },
        expat_f34d707150a648e198341f5783763ddb_release = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "2.7.1",
            includedirs = {
                "C:/msys64/mingw64/bin/../include"
            },
            libfiles = {
                [[C:\msys64\mingw64\bin\..\lib\libexpat.a]]
            },
            links = {
                "expat"
            }
        },
        glew_f1937cf4c620461cac69417973f0b9e2_release = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "2.2.0",
            includedirs = {
                "C:/msys64/mingw64/bin/../include"
            },
            libfiles = {
                [[C:\msys64\mingw64\bin\..\lib\libglew32.a]]
            },
            links = {
                "glew32"
            }
        },
        libiconv_eafd018709b3499f9ae890c71d9320de_release = {
            static = true,
            linkdirs = {
                "C:/msys64/mingw64/bin/../lib"
            },
            version = "1.18",
            includedirs = {
                "C:/msys64/mingw64/bin/../include"
            },
            libfiles = {
                [[C:\msys64\mingw64\bin\..\lib\libiconv.a]]
            },
            links = {
                "iconv"
            }
        }
    },
    find_program_mingw_arch_x86_64_plat_mingw_checktoolcc = {
        ["x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    ["detect.sdks.find_mingw"] = {
        mingw = {
            cross = "x86_64-w64-mingw32-",
            bindir = [[C:\msys64\mingw64\bin]],
            sdkdir = [[C:\msys64\mingw64]]
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["-save-temps"] = true,
            ["-Xpreprocessor"] = true,
            ["--version"] = true,
            ["-print-multi-lib"] = true,
            ["-pipe"] = true,
            ["-dumpspecs"] = true,
            ["--target-help"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-pie"] = true,
            ["-dumpversion"] = true,
            ["-v"] = true,
            ["-print-multiarch"] = true,
            ["-shared"] = true,
            ["-x"] = true,
            ["-Xassembler"] = true,
            ["--help"] = true,
            ["-c"] = true,
            ["-o"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-Xlinker"] = true,
            ["-time"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["--param"] = true,
            ["-print-multi-os-directory"] = true,
            ["-print-search-dirs"] = true,
            ["-B"] = true,
            ["-dumpmachine"] = true,
            ["-print-multi-directory"] = true,
            ["-print-sysroot"] = true,
            ["-pass-exit-codes"] = true,
            ["-S"] = true,
            ["-E"] = true
        },
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-gcc_15.1.0"] = {
            ["-save-temps"] = true,
            ["-Xpreprocessor"] = true,
            ["--version"] = true,
            ["-print-multi-lib"] = true,
            ["-pipe"] = true,
            ["-dumpspecs"] = true,
            ["--target-help"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-pie"] = true,
            ["-dumpversion"] = true,
            ["-v"] = true,
            ["-print-multiarch"] = true,
            ["-E"] = true,
            ["-print-sysroot"] = true,
            ["-Xassembler"] = true,
            ["-dumpmachine"] = true,
            ["-print-multi-os-directory"] = true,
            ["-o"] = true,
            ["--help"] = true,
            ["-Xlinker"] = true,
            ["-time"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["--param"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-B"] = true,
            ["-print-search-dirs"] = true,
            ["-c"] = true,
            ["-print-multi-directory"] = true,
            ["-x"] = true,
            ["-pass-exit-codes"] = true,
            ["-S"] = true,
            ["-shared"] = true
        }
    },
    find_programver = {
        ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = "19.44.35209",
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = "15.1.0",
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-gcc"] = "15.1.0"
    },
    find_package_mingw_x86_64_fetch_package_xmake = {
        ["xmake::expat_f34d707150a648e198341f5783763ddb_release_2.7.1"] = false,
        ["xmake::zlib_c7c23c5e1518475f91a7172443771802_release_v1.3.1"] = false,
        ["xmake::freetype_ea1882b923ab4271ad20e28023169ad1_release_2.13.1"] = false,
        ["xmake::glfw_2b0ee41504834bce92ad7417ca255952_release_3.4"] = false,
        ["xmake::fontconfig_62e53a1d83bd4fe388dd364c9b5903d6_release_2.14.2"] = false,
        ["xmake::glew_f1937cf4c620461cac69417973f0b9e2_release_2.2.0"] = false,
        ["xmake::libiconv_eafd018709b3499f9ae890c71d9320de_release_1.18"] = false
    },
    find_programver_fetch_package_system = {
        ["C:\\Program Files\\Meson\\meson.exe"] = "1.7.1",
        ["C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\python3.10.exe"] = "3.10.11",
        ["C:\\msys64\\mingw64\\bin\\ninja.exe"] = "1.12.1",
        ["C:\\Program Files\\CMake\\bin\\cmake"] = "4.0.1"
    }
}