{
    libiconv = {
        __enabled = true,
        libfiles = [[C:\msys64\mingw64\bin\..\lib\libiconv.a]],
        __requirestr = "libiconv",
        static = true,
        version = "1.18",
        linkdirs = "C:/msys64/mingw64/bin/../lib",
        links = "iconv",
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\l\libiconv\1.18\eafd018709b3499f9ae890c71d9320de\bin]]
            }
        },
        includedirs = "C:/msys64/mingw64/bin/../include"
    },
    fontconfig = {
        __enabled = true,
        libfiles = {
            [[C:\msys64\mingw64\bin\..\lib\libfontconfig.a]],
            [[C:\msys64\mingw64\lib\libfreetype.a]],
            [[C:\msys64\mingw64\lib\libfreetype.dll.a]],
            [[C:\msys64\mingw64\lib\libz.a]],
            [[C:\msys64\mingw64\lib\libz.dll.a]],
            [[C:\msys64\mingw64\bin\..\lib\libexpat.a]]
        },
        __requirestr = "fontconfig",
        static = true,
        version = "2.16.2",
        linkdirs = "C:/msys64/mingw64/bin/../lib",
        links = {
            "fontconfig",
            "freetype",
            "z",
            "expat"
        },
        envs = {
            PYTHONPATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\p\python\3.13.2\1ab060b131fc45718b2961e614d496c6\Lib\site-packages]]
            },
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\p\python\3.13.2\1ab060b131fc45718b2961e614d496c6\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\p\python\3.13.2\1ab060b131fc45718b2961e614d496c6\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\p\python\3.13.2\1ab060b131fc45718b2961e614d496c6\Scripts]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\m\meson\1.8.0\11b2c382111d405f91a8090845b3b0f4\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\4bf46eef4bbc49d49e59e81106446263\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.1\c99ebe0243284380b19aa0e1160a28ba\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\g\gperf\3.1\bfba88a8c72344fbbff93865e22a15d2\bin]]
            }
        },
        includedirs = {
            "C:/msys64/mingw64/bin/../include",
            "C:/msys64/mingw64/bin/../include/freetype2",
            "C:/msys64/mingw64/bin/../include/libpng16",
            "C:/msys64/mingw64/bin/../include/harfbuzz",
            "C:/msys64/mingw64/bin/../include/glib-2.0",
            "C:/msys64/mingw64/bin/../lib/glib-2.0/include"
        }
    },
    glew = {
        static = true,
        __enabled = true,
        includedirs = "C:/msys64/mingw64/bin/../include",
        linkdirs = "C:/msys64/mingw64/bin/../lib",
        __requirestr = "glew",
        libfiles = [[C:\msys64\mingw64\bin\..\lib\libglew32.a]],
        links = "glew32",
        version = "2.2.0"
    },
    opengl = {
        __requirestr = "opengl",
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\o\opengl\latest\dbf654cbf0394970a33564d98b8d4ac1]],
        links = "opengl32",
        __enabled = true
    },
    glfw = {
        __enabled = true,
        libfiles = {
            [[C:\msys64\mingw64\lib\libglfw3.a]],
            [[C:\msys64\mingw64\lib\libglfw3.dll.a]]
        },
        __requirestr = "glfw",
        static = true,
        version = "3.4.0",
        linkdirs = "C:/msys64/mingw64/bin/../lib",
        links = {
            "glfw3",
            "opengl32"
        },
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\4bf46eef4bbc49d49e59e81106446263\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.1\c99ebe0243284380b19aa0e1160a28ba\bin]]
            }
        },
        includedirs = "C:/msys64/mingw64/bin/../include"
    },
    freetype = {
        __enabled = true,
        libfiles = {
            [[C:\msys64\mingw64\lib\libfreetype.a]],
            [[C:\msys64\mingw64\lib\libfreetype.dll.a]],
            [[C:\msys64\mingw64\lib\libz.a]],
            [[C:\msys64\mingw64\lib\libz.dll.a]]
        },
        __requirestr = "freetype",
        static = true,
        version = "26.2.20",
        linkdirs = "C:/msys64/mingw64/bin/../lib",
        links = {
            "freetype",
            "z"
        },
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\4bf46eef4bbc49d49e59e81106446263\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.1\c99ebe0243284380b19aa0e1160a28ba\bin]]
            }
        },
        includedirs = {
            "C:/msys64/mingw64/bin/../include/freetype2",
            "C:/msys64/mingw64/bin/../include",
            "C:/msys64/mingw64/bin/../include/libpng16",
            "C:/msys64/mingw64/bin/../include/harfbuzz",
            "C:/msys64/mingw64/bin/../include/glib-2.0",
            "C:/msys64/mingw64/bin/../lib/glib-2.0/include"
        }
    }
}