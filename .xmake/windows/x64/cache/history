{
    cmdlines = {
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake ",
        [[xmake lua C:\Users\<USER>\xmake\actions\build\cleaner.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\update_intellisense.lua .vscode clangd]],
        "xmake clean",
        "xmake ",
        "xmake run",
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\default_target.lua]],
        "xmake f -p mingw -a x86_64 -m release",
        "xmake run autoclicker",
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.vscode\extensions\tboox.xmake-vscode-2.4.0\assets\update_intellisense.lua .vscode clangd]]
    }
}