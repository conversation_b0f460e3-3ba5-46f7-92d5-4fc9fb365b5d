{
    cmdlines = {
        "xmake check",
        "xmake clean",
        "xmake f -p linux -m debug",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake f -p linux -m debug",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode clangd",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake check",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake ",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/modules/private/utils/statistics.lua",
        "xmake lua /nix/store/spfgnq88drzmslqsx43rvbysdh2myn5p-xmake-2.9.9/share/xmake/actions/build/cleaner.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua"
    }
}