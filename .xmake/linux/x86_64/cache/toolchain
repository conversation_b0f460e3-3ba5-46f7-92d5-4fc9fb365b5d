{
    nasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    nim_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = false,
        arch = "x86_64"
    },
    swift_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    fpc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    tool_target_autoclicker_linux_x86_64_cxx = {
        toolchain_info = {
            plat = "linux",
            cachekey = "envs_arch_x86_64_plat_linux",
            arch = "x86_64",
            name = "envs"
        },
        toolname = "gxx",
        program = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"
    },
    go_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    gcc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = {
            name = "gcc",
            program = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/gcc"
        },
        arch = "x86_64"
    },
    fasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    gfortran_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    rust_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    cross_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = false,
        arch = "x86_64"
    },
    cuda_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    envs_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    },
    tool_target_autoclicker_linux_x86_64_ld = {
        toolchain_info = {
            plat = "linux",
            name = "envs",
            arch = "x86_64",
            cachekey = "envs_arch_x86_64_plat_linux"
        },
        toolname = "gxx",
        program = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"
    },
    yasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        __checked = true,
        arch = "x86_64"
    }
}