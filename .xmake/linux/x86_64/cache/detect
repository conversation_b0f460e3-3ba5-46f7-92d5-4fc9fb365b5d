{
    find_program_envs_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"
    },
    find_programver = {
        ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"] = "14.2.1"
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxxflags_-m64_-fstack-protector-all"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxxflags_-m64_-O3"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxxflags_-m64_-g3"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxflags_-m64_-fvisibility-inlines-hidden"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxflags_-m64_-MMD -MF"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_ld_ldflags_-m64 -m64_-O3"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx__-m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx__-m64_-O0"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_cxx_cxxflags_-m64_-fno-omit-frame-pointer"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1_ld_ldflags_-m64 -m64_-g3"] = true
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1"] = {
            ["-pass-exit-codes"] = true,
            ["-dumpversion"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-print-multi-lib"] = true,
            ["-dumpspecs"] = true,
            ["-pipe"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-print-search-dirs"] = true,
            ["-print-sysroot"] = true,
            ["-o"] = true,
            ["--help"] = true,
            ["-print-multi-os-directory"] = true,
            ["-save-temps"] = true,
            ["-shared"] = true,
            ["-time"] = true,
            ["--version"] = true,
            ["-print-multiarch"] = true,
            ["--target-help"] = true,
            ["-Xlinker"] = true,
            ["-print-multi-directory"] = true,
            ["-Xassembler"] = true,
            ["-Xpreprocessor"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-B"] = true,
            ["-dumpmachine"] = true,
            ["-E"] = true,
            ["--param"] = true,
            ["-x"] = true,
            ["-S"] = true,
            ["-pie"] = true,
            ["-v"] = true,
            ["-c"] = true
        }
    },
    find_program = {
        git = "/run/current-system/sw/bin/git",
        ping = "/run/current-system/sw/bin/ping",
        gcc = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/gcc",
        ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"] = "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++"
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++_14.2.1"] = {
            ["-Tbss"] = true,
            ["-f"] = true,
            ["-Bgroup"] = true,
            ["-nostdlib"] = true,
            ["--print-map-discarded"] = true,
            ["-m"] = true,
            ["--oformat"] = true,
            ["-Ttext"] = true,
            ["-rpath-link"] = true,
            ["-Qy"] = true,
            ["--warn-textrel"] = true,
            ["-Bshareable"] = true,
            ["--ctf-variables"] = true,
            ["--no-whole-archive"] = true,
            ["-EB"] = true,
            ["-fini"] = true,
            ["--format"] = true,
            ["--no-gc-sections"] = true,
            ["-u"] = true,
            ["--undefined-version"] = true,
            ["-e"] = true,
            ["--default-imported-symver"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--no-keep-memory"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--no-warn-execstack"] = true,
            ["--no-export-dynamic"] = true,
            ["--no-print-map-discarded"] = true,
            ["--dynamic-list-data"] = true,
            ["-dp"] = true,
            ["-init"] = true,
            ["--start-group"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--task-link"] = true,
            ["--version-script"] = true,
            ["--no-define-common"] = true,
            ["--remap-inputs-file"] = true,
            ["-debug"] = true,
            ["--export-dynamic"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--no-dynamic-linker"] = true,
            ["--warn-section-align"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["--architecture"] = true,
            ["--check-sections"] = true,
            ["--library"] = true,
            ["--warn-alternate-em"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--relax"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--no-strip-discarded"] = true,
            ["-P"] = true,
            ["--gc-keep-exported"] = true,
            ["--trace-symbol"] = true,
            ["-g"] = true,
            ["--map-whole-files"] = true,
            ["--allow-multiple-definition"] = true,
            ["--auxiliary"] = true,
            ["--no-warnings"] = true,
            ["--end-group"] = true,
            ["--disable-linker-version"] = true,
            ["--fatal-warnings"] = true,
            ["-h"] = true,
            ["--wrap"] = true,
            ["--no-omagic"] = true,
            ["-Bno-symbolic"] = true,
            ["--undefined"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-print-gc-sections"] = true,
            ["-A"] = true,
            ["-dT"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--unique"] = true,
            ["--trace"] = true,
            ["-Ttext-segment"] = true,
            ["-plugin-opt"] = true,
            ["--discard-all"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--filter"] = true,
            ["--force-exe-suffix"] = true,
            ["--relocatable"] = true,
            ["-y"] = true,
            ["--gpsize"] = true,
            ["--pop-state"] = true,
            ["--just-symbols"] = true,
            ["--print-gc-sections"] = true,
            ["-static"] = true,
            ["--no-as-needed"] = true,
            ["--library-path"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["--warn-multiple-gp"] = true,
            ["-a"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--error-execstack"] = true,
            ["--no-relax"] = true,
            ["--no-fatal-warnings"] = true,
            ["-V"] = true,
            ["--eh-frame-hdr"] = true,
            ["--no-check-sections"] = true,
            ["--warn-once"] = true,
            ["--no-undefined-version"] = true,
            ["-Ur"] = true,
            ["-rpath"] = true,
            ["--entry"] = true,
            ["--default-script"] = true,
            ["--print-memory-usage"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--image-base"] = true,
            ["-I"] = true,
            ["--disable-new-dtags"] = true,
            ["-R"] = true,
            ["--emit-relocs"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["-flto"] = true,
            ["--out-implib"] = true,
            ["--strip-discarded"] = true,
            ["-z"] = true,
            ["--export-dynamic-symbol"] = true,
            ["-O"] = true,
            ["--print-output-format"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["-Y"] = true,
            ["--print-map"] = true,
            ["--help"] = true,
            ["-plugin-save-temps"] = true,
            ["--force-group-allocation"] = true,
            ["-Tdata"] = true,
            ["--error-handling-script"] = true,
            ["--strip-debug"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--cref"] = true,
            ["--traditional-format"] = true,
            ["--no-print-map-locals"] = true,
            ["--mri-script"] = true,
            ["--print-sysroot"] = true,
            ["-EL"] = true,
            ["-plugin"] = true,
            ["--dependency-file"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--discard-none"] = true,
            ["--split-by-reloc"] = true,
            ["-b"] = true,
            ["--omagic"] = true,
            ["--section-ordering-file"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--output"] = true,
            ["--sort-common"] = true,
            ["--no-demangle"] = true,
            ["-F"] = true,
            ["--no-warn-mismatch"] = true,
            ["--orphan-handling"] = true,
            ["-G"] = true,
            ["--retain-symbols-file"] = true,
            ["--error-rwx-segments"] = true,
            ["--script"] = true,
            ["-qmagic"] = true,
            ["-Trodata-segment"] = true,
            ["--no-ctf-variables"] = true,
            ["--whole-archive"] = true,
            ["--gc-sections"] = true,
            ["--enable-new-dtags"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--default-symver"] = true,
            ["-L"] = true,
            ["--version-exports-section"] = true,
            ["-T"] = true,
            ["-assert"] = true,
            ["-no-pie"] = true,
            ["-soname"] = true,
            ["--demangle"] = true,
            ["-Bsymbolic"] = true,
            ["--require-defined"] = true,
            ["--split-by-file"] = true,
            ["-Tldata-segment"] = true,
            ["--target-help"] = true,
            ["--enable-linker-version"] = true,
            ["-Map"] = true,
            ["-c"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--push-state"] = true,
            ["--defsym"] = true,
            ["--version"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--rosegment"] = true,
            ["--no-rosegment"] = true,
            ["--no-map-whole-files"] = true,
            ["--no-error-rwx-segments"] = true,
            ["-l"] = true,
            ["--no-undefined"] = true,
            ["--dynamic-linker"] = true,
            ["--dynamic-list"] = true,
            ["--warn-execstack"] = true,
            ["--discard-locals"] = true,
            ["--strip-all"] = true,
            ["--warn-execstack-objects"] = true,
            ["--print-map-locals"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--verbose"] = true,
            ["--warn-common"] = true,
            ["--section-start"] = true,
            ["--warn-rwx-segments"] = true,
            ["--as-needed"] = true,
            ["--stats"] = true,
            ["--sort-section"] = true,
            ["--pic-executable"] = true,
            ["--nmagic"] = true,
            ["--no-error-execstack"] = true,
            ["--remap-inputs"] = true,
            ["-o"] = true
        }
    }
}