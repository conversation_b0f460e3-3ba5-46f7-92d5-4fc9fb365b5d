{
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/run/current-system/sw/bin/gcc_14.2.1_cc__-m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false,
        ["linux_x86_64_/run/current-system/sw/bin/gcc_14.2.1_cc__-m64 -m64 -target x86_64-linux-gnu_-O3 -mtune=native -march=native -mfpmath=both"] = false,
        ["linux_x86_64_/run/current-system/sw/bin/g++_14.2.1_ld__-m64 -m64 -target x86_64-linux-gnu -m64 -m64 -target x86_64-linux-gnu_-O3 -mtune=native -march=native -mfpmath=both"] = false,
        ["linux_x86_64_/run/current-system/sw/bin/gcc_14.2.1_cc__-m64 -m64 -target x86_64-linux-gnu_-fopenmp"] = false,
        ["linux_x86_64_/run/current-system/sw/bin/g++_14.2.1_ld__-m64 -m64 -target x86_64-linux-gnu -m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false,
        ["linux_x86_64_/run/current-system/sw/bin/g++_14.2.1_ld__-m64 -m64 -target x86_64-linux-gnu -m64 -m64 -target x86_64-linux-gnu_-fopenmp"] = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/run/current-system/sw/bin/g++_14.2.1"] = {
            ["--format"] = true,
            ["-Tdata"] = true,
            ["--unique"] = true,
            ["-f"] = true,
            ["--trace"] = true,
            ["-rpath"] = true,
            ["--check-sections"] = true,
            ["-o"] = true,
            ["-Trodata-segment"] = true,
            ["--print-output-format"] = true,
            ["--dynamic-linker"] = true,
            ["-init"] = true,
            ["--map-whole-files"] = true,
            ["--wrap"] = true,
            ["--undefined-version"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--no-warnings"] = true,
            ["--no-keep-memory"] = true,
            ["--no-fatal-warnings"] = true,
            ["--library"] = true,
            ["--undefined"] = true,
            ["-O"] = true,
            ["-g"] = true,
            ["--mri-script"] = true,
            ["--default-symver"] = true,
            ["--no-undefined"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["-G"] = true,
            ["--no-relax"] = true,
            ["-Qy"] = true,
            ["--whole-archive"] = true,
            ["-debug"] = true,
            ["--pic-executable"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--strip-all"] = true,
            ["--defsym"] = true,
            ["--print-gc-sections"] = true,
            ["--force-group-allocation"] = true,
            ["--no-as-needed"] = true,
            ["--pop-state"] = true,
            ["-Bno-symbolic"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--warn-execstack-objects"] = true,
            ["-fini"] = true,
            ["-P"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--no-error-rwx-segments"] = true,
            ["--print-map"] = true,
            ["-plugin"] = true,
            ["-F"] = true,
            ["--no-demangle"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--sort-section"] = true,
            ["--script"] = true,
            ["--remap-inputs-file"] = true,
            ["--eh-frame-hdr"] = true,
            ["--target-help"] = true,
            ["-h"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--print-map-discarded"] = true,
            ["--no-check-sections"] = true,
            ["--error-handling-script"] = true,
            ["--version-script"] = true,
            ["-Y"] = true,
            ["--warn-textrel"] = true,
            ["-a"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--discard-none"] = true,
            ["--no-dynamic-linker"] = true,
            ["-y"] = true,
            ["--remap-inputs"] = true,
            ["--default-imported-symver"] = true,
            ["-Map"] = true,
            ["--gc-keep-exported"] = true,
            ["--no-warn-execstack"] = true,
            ["-dT"] = true,
            ["-assert"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--warn-common"] = true,
            ["--dependency-file"] = true,
            ["--end-group"] = true,
            ["--gc-sections"] = true,
            ["--start-group"] = true,
            ["-Ur"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--rosegment"] = true,
            ["-R"] = true,
            ["--split-by-file"] = true,
            ["--trace-symbol"] = true,
            ["-flto"] = true,
            ["-b"] = true,
            ["--warn-section-align"] = true,
            ["--no-print-gc-sections"] = true,
            ["-A"] = true,
            ["-V"] = true,
            ["--force-exe-suffix"] = true,
            ["--no-export-dynamic"] = true,
            ["--no-error-execstack"] = true,
            ["--verbose"] = true,
            ["--strip-discarded"] = true,
            ["--dynamic-list"] = true,
            ["--error-rwx-segments"] = true,
            ["--require-defined"] = true,
            ["-EL"] = true,
            ["--no-omagic"] = true,
            ["--no-strip-discarded"] = true,
            ["--warn-execstack"] = true,
            ["--oformat"] = true,
            ["--retain-symbols-file"] = true,
            ["--fatal-warnings"] = true,
            ["--cref"] = true,
            ["--out-implib"] = true,
            ["--no-gc-sections"] = true,
            ["-Bgroup"] = true,
            ["--version-exports-section"] = true,
            ["--demangle"] = true,
            ["--relocatable"] = true,
            ["--just-symbols"] = true,
            ["--gpsize"] = true,
            ["--disable-linker-version"] = true,
            ["-Ttext"] = true,
            ["-I"] = true,
            ["--help"] = true,
            ["--no-undefined-version"] = true,
            ["-static"] = true,
            ["--image-base"] = true,
            ["--section-start"] = true,
            ["--print-sysroot"] = true,
            ["--error-execstack"] = true,
            ["--disable-new-dtags"] = true,
            ["--traditional-format"] = true,
            ["--no-print-map-discarded"] = true,
            ["--task-link"] = true,
            ["--no-whole-archive"] = true,
            ["--default-script"] = true,
            ["--no-warn-mismatch"] = true,
            ["-nostdlib"] = true,
            ["--omagic"] = true,
            ["-plugin-opt"] = true,
            ["--discard-all"] = true,
            ["--ctf-variables"] = true,
            ["--sort-common"] = true,
            ["-plugin-save-temps"] = true,
            ["--version"] = true,
            ["-Bsymbolic-functions"] = true,
            ["-EB"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["-L"] = true,
            ["-u"] = true,
            ["--print-map-locals"] = true,
            ["--no-define-common"] = true,
            ["--architecture"] = true,
            ["--section-ordering-file"] = true,
            ["--emit-relocs"] = true,
            ["-m"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["-dp"] = true,
            ["-T"] = true,
            ["--no-map-whole-files"] = true,
            ["--discard-locals"] = true,
            ["--stats"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--output"] = true,
            ["--strip-debug"] = true,
            ["--allow-multiple-definition"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["-c"] = true,
            ["--push-state"] = true,
            ["--enable-linker-version"] = true,
            ["-rpath-link"] = true,
            ["-no-pie"] = true,
            ["--entry"] = true,
            ["--auxiliary"] = true,
            ["--export-dynamic"] = true,
            ["-qmagic"] = true,
            ["--nmagic"] = true,
            ["--library-path"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--as-needed"] = true,
            ["--enable-new-dtags"] = true,
            ["--print-memory-usage"] = true,
            ["-Tbss"] = true,
            ["--warn-once"] = true,
            ["-e"] = true,
            ["-Tldata-segment"] = true,
            ["-Bsymbolic"] = true,
            ["-l"] = true,
            ["--filter"] = true,
            ["--dynamic-list-data"] = true,
            ["--warn-rwx-segments"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--split-by-reloc"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--no-rosegment"] = true,
            ["-z"] = true,
            ["--warn-multiple-gp"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["-soname"] = true,
            ["--no-print-map-locals"] = true,
            ["-Ttext-segment"] = true,
            ["--orphan-handling"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["-Bshareable"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--warn-alternate-em"] = true,
            ["--relax"] = true,
            ["--no-ctf-variables"] = true
        }
    },
    find_program = {
        ["/run/current-system/sw/bin/g++"] = "/run/current-system/sw/bin/g++",
        ["/run/current-system/sw/bin/gcc"] = "/run/current-system/sw/bin/gcc"
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/run/current-system/sw/bin/gcc"
    },
    find_programver = {
        ["/run/current-system/sw/bin/g++"] = "14.2.1",
        ["/run/current-system/sw/bin/gcc"] = "14.2.1"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/run/current-system/sw/bin/gcc_14.2.1"] = {
            ["-no-canonical-prefixes"] = true,
            ["-dumpversion"] = true,
            ["-pie"] = true,
            ["--help"] = true,
            ["-print-multiarch"] = true,
            ["-S"] = true,
            ["-B"] = true,
            ["--version"] = true,
            ["-dumpspecs"] = true,
            ["-c"] = true,
            ["-o"] = true,
            ["-E"] = true,
            ["-dumpmachine"] = true,
            ["-x"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["--target-help"] = true,
            ["-v"] = true,
            ["-save-temps"] = true,
            ["-print-multi-os-directory"] = true,
            ["-pipe"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-pass-exit-codes"] = true,
            ["-shared"] = true,
            ["-Xlinker"] = true,
            ["-print-multi-directory"] = true,
            ["-Xpreprocessor"] = true,
            ["-Xassembler"] = true,
            ["--param"] = true,
            ["-print-sysroot"] = true,
            ["-time"] = true,
            ["-print-multi-lib"] = true,
            ["-print-search-dirs"] = true
        }
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/run/current-system/sw/bin/g++"
    }
}