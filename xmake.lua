add_rules("mode.debug", "mode.release")

-- プロジェクト設定
set_project("autoclicker")
set_version("1.0.0")
set_languages("c99", "c11", "cxx11", "cxx17")

-- コンパイラの設定（Mingw）
if is_plat("windows") then
    -- MinGWの明示的なパスを指定
    set_toolchains("mingw")
end

-- システムにインストールされたライブラリを使用
if not is_plat("linux") then
    -- Windows/その他のプラットフォーム用
    add_requires("libiconv")
    add_requires("freetype")
    add_requires("fontconfig")
    add_requires("glfw")
    add_requires("opengl")
    add_requires("glew")
end

if is_mode("debug") then
    set_config("debugger", "gdb") -- 新しい構文
    print("Debug mode: debugger should be called, using gdb.")
end

-- アプリケーションのターゲット設定
target("autoclicker")
    set_kind("binary")
    add_rules("utils.bin2c", {extensions = {".png", ".vs", ".fs", ".ttf"}})
    
    -- Warning and optimization settings
    if is_plat("linux") then
        add_cflags("-O3")
        add_cxxflags("-O3")
        add_ldflags("-O3")
    else
        add_cflags("-O3 -mtune=native -march=native -mfpmath=both")
        add_cxxflags("-O3 -mtune=native -march=native -mfpmath=both")
        add_ldflags("-O3 -mtune=native -march=native -mfpmath=both")
    end

    -- Settings for GDB debugging
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_cflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_cxxflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_ldflags("-g3")
        add_defines("DEBUG", "DEBUG_GL", "GL_DEBUG")
        print("Debug mode: target debug mode called.")

        -- デバッグ情報を強化
        if is_plat("windows", "mingw") then
            add_cxflags("-gdwarf-4")  -- DWARF形式のデバッグ情報
            add_ldflags("-Wl,--export-all-symbols") -- すべてのシンボルをエクスポート
        end
    end    -- ソースファイルの追加
    add_files("src/core/*.cpp")
    add_files("src/imgui/*.cpp")
    -- C++コンパイルフラグの追加
    set_languages("cxx17")
    -- add_files("resources/textures/*.png")
    -- add_files("resources/fonts/*.ttf")
    -- シェーダーファイルが存在しない場合はコメントアウト
    -- add_files("resources/shaders/*.vs")
    -- add_files("resources/shaders/*.fs")

    -- インクルードパスとライブラリ設定
    add_includedirs("include") -- include ディレクトリ全体をインクルードパスに追加
    add_includedirs("include/imgui") -- imgui のヘッダーファイル用

    -- プラットフォーム別パッケージ設定
    if not is_plat("linux") then
        add_packages("libiconv", "freetype", "fontconfig","glfw", "opengl", "glew")
    end

    -- OpenGLのライブラリを追加
    if is_plat("windows", "mingw") then
        add_links("opengl32", "glu32")
    elseif is_plat("linux") then
        add_links("GL", "X11", "Xtst", "Xext", "glfw")
    elseif is_plat("macosx") then
        add_frameworks("OpenGL")
    end
    add_defines("PLATFORM_DESKTOP")

    -- プラットフォーム別の定義
    if is_plat("linux") then
        add_defines("PLATFORM_LINUX", "IMGUI_IMPL_OPENGL_LOADER_GL3W")
    elseif is_plat("windows", "mingw") then
        add_defines("PLATFORM_WINDOWS", "IMGUI_IMPL_OPENGL_LOADER_GLEW")
    else
        add_defines("IMGUI_IMPL_OPENGL_LOADER_GLEW")
    end    -- プラットフォーム別設定
    if is_plat("windows", "mingw") then
        add_syslinks("ole32", "user32", "gdi32", "comdlg32", "pthread", "uuid")
        -- Windows GUIサブシステムを設定
        if is_plat("windows") then
            add_ldflags("-mwindows")
        end
    elseif is_plat("linux") then
        add_syslinks("pthread", "rt")
    else
        add_syslinks("pthread")
    end
