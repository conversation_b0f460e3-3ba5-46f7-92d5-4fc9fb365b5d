[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/core/main.cpp.o", "src/core/main.cpp"], "file": "src/core/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/core/platform_linux.cpp.o", "src/core/platform_linux.cpp"], "file": "src/core/platform_linux.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/core/platform_windows.cpp.o", "src/core/platform_windows.cpp"], "file": "src/core/platform_windows.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui.cpp.o", "src/imgui/imgui.cpp"], "file": "src/imgui/imgui.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui_draw.cpp.o", "src/imgui/imgui_draw.cpp"], "file": "src/imgui/imgui_draw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui_impl_glfw.cpp.o", "src/imgui/imgui_impl_glfw.cpp"], "file": "src/imgui/imgui_impl_glfw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui_impl_opengl3.cpp.o", "src/imgui/imgui_impl_opengl3.cpp"], "file": "src/imgui/imgui_impl_opengl3.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui_tables.cpp.o", "src/imgui/imgui_tables.cpp"], "file": "src/imgui/imgui_tables.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/platform/windows/idl", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-o", "build/.objs/autoclicker/linux/x86_64/src/imgui/imgui_widgets.cpp.o", "src/imgui/imgui_widgets.cpp"], "file": "src/imgui/imgui_widgets.cpp"}]