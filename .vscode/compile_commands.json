[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/core/main.cpp.o", "src/core/main.cpp"], "file": "src/core/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/core/platform_linux.cpp.o", "src/core/platform_linux.cpp"], "file": "src/core/platform_linux.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/core/platform_windows.cpp.o", "src/core/platform_windows.cpp"], "file": "src/core/platform_windows.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui.cpp.o", "src/imgui/imgui.cpp"], "file": "src/imgui/imgui.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_draw.cpp.o", "src/imgui/imgui_draw.cpp"], "file": "src/imgui/imgui_draw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_impl_glfw.cpp.o", "src/imgui/imgui_impl_glfw.cpp"], "file": "src/imgui/imgui_impl_glfw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_impl_opengl3.cpp.o", "src/imgui/imgui_impl_opengl3.cpp"], "file": "src/imgui/imgui_impl_opengl3.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_tables.cpp.o", "src/imgui/imgui_tables.cpp"], "file": "src/imgui/imgui_tables.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/autoclicker_c", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-g", "-O0", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/rules/utils/bin2c", "-Ibuild/.gens/autoclicker/linux/x86_64/debug/platform/windows/idl", "-DDEBUG", "-DDEBUG_GL", "-DGL_DEBUG", "-DPLATFORM_DESKTOP", "-DPLATFORM_LINUX", "-DIMGUI_IMPL_OPENGL_LOADER_GL3W", "-O3", "-g3", "-fstack-protector-all", "-fno-omit-frame-pointer", "-o", "build/.objs/autoclicker/linux/x86_64/debug/src/imgui/imgui_widgets.cpp.o", "src/imgui/imgui_widgets.cpp"], "file": "src/imgui/imgui_widgets.cpp"}]